<?php

namespace App\Filament\Imports;

use App\Models\Cargo;
use App\Models\Driver;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;

class DriverImporter extends Importer
{
    protected static ?string $model = Driver::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('name')
                ->label('Nombre')
                ->requiredMapping()
                ->rules(['required', 'max:255']),

            ImportColumn::make('last_paternal_name')
                ->label('Apellido Paterno')
                ->requiredMapping()
                ->rules(['required', 'max:255']),

            ImportColumn::make('last_maternal_name')
                ->label('Apellido Materno')
                ->requiredMapping()
                ->rules(['required', 'max:255']),

            ImportColumn::make('dni')
                ->label('DNI')
                ->requiredMapping()
                ->numeric()
                ->rules(['required', 'integer']),

            ImportColumn::make('cargo')
                ->label('Cargo')
                ->requiredMapping()
                ->rules(['required'])
                ->example('Conductor, Supervisor, etc.'),

            ImportColumn::make('file')
                ->label('Archivo')
                ->rules(['nullable', 'max:255']),

            ImportColumn::make('status')
                ->label('Estado')
                ->boolean()
                ->rules(['boolean'])
                ->default(true),
        ];
    }

    public function resolveRecord(): ?Driver
    {
        // Buscar el cargo por nombre y obtener su ID
        $cargo = Cargo::where('name', $this->data['cargo'])->first();

        if (!$cargo) {
            // Si no existe el cargo, crear uno nuevo
            $cargo = Cargo::create([
                'name' => $this->data['cargo'],
                'status' => true,
            ]);
        }

        // Buscar si ya existe un conductor con el mismo DNI
        return Driver::firstOrNew([
            'dni' => $this->data['dni'],
        ], [
            'name' => $this->data['name'],
            'last_paternal_name' => $this->data['last_paternal_name'],
            'last_maternal_name' => $this->data['last_maternal_name'],
            'cargo_id' => $cargo->id,
            'file' => $this->data['file'] ?? null,
            'status' => $this->data['status'] ?? true,
        ]);
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'La importación de conductores se ha completado y ' . number_format($import->successful_rows) . ' ' . str('fila')->plural($import->successful_rows) . ' fueron importadas.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('fila')->plural($failedRowsCount) . ' fallaron al importar.';
        }

        return $body;
    }
}
